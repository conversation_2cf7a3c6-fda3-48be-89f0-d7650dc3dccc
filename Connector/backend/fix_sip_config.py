#!/usr/bin/env python3
"""
Fix LiveKit SIP configuration to enable incoming call routing
"""

import os
import asyncio
from dotenv import load_dotenv
from livekit import api

# Load environment variables
load_dotenv()

async def fix_sip_configuration():
    """Fix SIP configuration to enable incoming call routing"""
    
    # LiveKit credentials
    livekit_url = os.environ.get('LIVEKIT_URL')
    livekit_api_key = os.environ.get('LIVEKIT_API_KEY')
    livekit_api_secret = os.environ.get('LIVEKIT_API_SECRET')
    sip_trunk_id = os.environ.get('SIP_INBOUND_TRUNK_ID')
    
    if not all([livekit_url, livekit_api_key, livekit_api_secret, sip_trunk_id]):
        print("❌ Missing required environment variables")
        return False
    
    print(f"🔧 Fixing SIP configuration for trunk: {sip_trunk_id}")
    
    # Create LiveKit API client
    livekit_api = api.LiveKitAPI(
        url=livekit_url,
        api_key=livekit_api_key,
        api_secret=livekit_api_secret
    )
    
    try:
        # Step 1: Update SIP trunk with phone numbers
        print("\n📞 Step 1: Adding phone numbers to SIP trunk...")
        
        # Get business phone numbers from database
        from db_driver import get_driver
        driver = get_driver()
        
        # Get all business phone numbers
        businesses = driver.client.table("businesses").select("phone").execute()
        phone_numbers = []
        for business in businesses.data:
            if business.get('phone'):
                phone = business['phone'].strip()
                if phone and phone not in phone_numbers:
                    phone_numbers.append(phone)
        
        print(f"Found {len(phone_numbers)} business phone numbers:")
        for phone in phone_numbers:
            print(f"  - {phone}")
        
        if not phone_numbers:
            print("❌ No phone numbers found in database!")
            return False
        
        # Update SIP trunk with phone numbers
        from livekit.api import UpdateSIPInboundTrunkRequest
        
        try:
            updated_trunk = await livekit_api.sip.update_sip_inbound_trunk(
                UpdateSIPInboundTrunkRequest(
                    sip_trunk_id=sip_trunk_id,
                    numbers=phone_numbers
                )
            )
            print(f"✅ Updated SIP trunk with {len(phone_numbers)} phone numbers")
        except Exception as e:
            print(f"❌ Error updating SIP trunk: {e}")
            return False
        
        # Step 2: Update dispatch rule to link with trunk
        print("\n🔀 Step 2: Linking dispatch rule to SIP trunk...")
        
        # Get existing dispatch rule
        from livekit.api import ListSIPDispatchRuleRequest
        rules_response = await livekit_api.sip.list_sip_dispatch_rule(
            ListSIPDispatchRuleRequest()
        )
        
        if not rules_response.items:
            print("❌ No dispatch rules found!")
            return False
        
        dispatch_rule = rules_response.items[0]  # Use the first rule
        rule_id = dispatch_rule.sip_dispatch_rule_id
        
        print(f"Found dispatch rule: {rule_id}")
        
        # Update dispatch rule to include our trunk
        from livekit.api import UpdateSIPDispatchRuleRequest
        
        try:
            updated_rule = await livekit_api.sip.update_sip_dispatch_rule(
                UpdateSIPDispatchRuleRequest(
                    sip_dispatch_rule_id=rule_id,
                    trunk_ids=[sip_trunk_id],
                    room_name="call-{to}-{from}",  # Room name pattern
                    metadata={
                        "webhook_url": "http://localhost:5001/sip/webhook",  # Your webhook URL
                        "description": "Route incoming calls to Connecto AI agents"
                    }
                )
            )
            print(f"✅ Updated dispatch rule to use trunk {sip_trunk_id}")
        except Exception as e:
            print(f"❌ Error updating dispatch rule: {e}")
            return False
        
        # Step 3: Verify configuration
        print("\n✅ Step 3: Verifying configuration...")
        
        # List updated trunk
        from livekit.api import ListSIPInboundTrunkRequest
        trunks_response = await livekit_api.sip.list_sip_inbound_trunk(
            ListSIPInboundTrunkRequest()
        )
        
        for trunk in trunks_response.items:
            if trunk.sip_trunk_id == sip_trunk_id:
                print(f"📞 SIP Trunk: {trunk.sip_trunk_id}")
                print(f"   Name: {getattr(trunk, 'name', 'N/A')}")
                print(f"   Numbers: {getattr(trunk, 'numbers', [])}")
                break
        
        # List updated rules
        rules_response = await livekit_api.sip.list_sip_dispatch_rule(
            ListSIPDispatchRuleRequest()
        )
        
        for rule in rules_response.items:
            if rule.sip_dispatch_rule_id == rule_id:
                print(f"🔀 Dispatch Rule: {rule.sip_dispatch_rule_id}")
                print(f"   Trunk IDs: {getattr(rule, 'trunk_ids', [])}")
                print(f"   Room Name: {getattr(rule, 'room_name', 'N/A')}")
                break
        
        print("\n🎉 SIP configuration fixed successfully!")
        print("\n📝 What happens now:")
        print("1. Incoming calls to your business numbers will be routed to LiveKit")
        print("2. LiveKit will create rooms and call your webhook")
        print("3. Your webhook will start AI agents to handle the calls")
        print("\n⚠️  Important: Your webhook URL must be publicly accessible!")
        print("   Current webhook: http://localhost:5001/sip/webhook")
        print("   Consider using ngrok or deploying to a public server")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing SIP configuration: {e}")
        return False

if __name__ == "__main__":
    print("🛠️  LiveKit SIP Configuration Fix Tool")
    print("=" * 50)
    
    # Run configuration fix
    success = asyncio.run(fix_sip_configuration())
    
    if success:
        print("\n✅ Configuration fix completed!")
    else:
        print("\n❌ Configuration fix failed!")
