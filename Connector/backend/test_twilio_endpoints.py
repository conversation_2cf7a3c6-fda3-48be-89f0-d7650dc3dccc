#!/usr/bin/env python3
"""
Test Twilio endpoints without requiring actual Twilio credentials
"""

import os
import json
import requests
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_flask_backend():
    """Test if Flask backend is running"""
    try:
        response = requests.get('http://localhost:5001/health', timeout=5)
        if response.status_code == 200:
            print("✅ Flask backend is running")
            return True
        else:
            print(f"❌ Flask backend returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Flask backend not accessible: {e}")
        return False

def get_ngrok_url():
    """Get the current ngrok tunnel URL"""
    try:
        response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
        tunnels = response.json()
        if tunnels.get('tunnels'):
            tunnel = tunnels['tunnels'][0]
            public_url = tunnel['public_url']
            print(f"✅ ngrok tunnel found: {public_url}")
            return public_url
        else:
            print("❌ No ngrok tunnels found")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ ngrok not accessible: {e}")
        return None

def test_twilio_voice_webhook(webhook_url):
    """Test the Twilio voice webhook endpoint"""
    print(f"\n🧪 Testing Twilio voice webhook: {webhook_url}/twilio/voice")
    
    # Simulate Twilio webhook payload
    test_payload = {
        'From': '+**********',  # Caller number
        'To': '+***********',   # Business number (existing in DB)
        'CallSid': f'test-call-{int(time.time())}',
        'Direction': 'inbound',
        'CallStatus': 'ringing'
    }
    
    try:
        response = requests.post(
            f"{webhook_url}/twilio/voice",
            data=test_payload,  # Twilio sends form data
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "ngrok-skip-browser-warning": "true"
            },
            timeout=30
        )
        
        print(f"📞 Webhook response status: {response.status_code}")
        print(f"📄 Response content type: {response.headers.get('content-type', 'unknown')}")
        print(f"📄 Response body: {response.text[:500]}...")
        
        if response.status_code == 200:
            # Check if response is TwiML
            if 'xml' in response.headers.get('content-type', ''):
                print("✅ Webhook returned TwiML response")
                return True
            else:
                print("⚠️  Webhook responded but not with TwiML")
                return False
        else:
            print(f"❌ Webhook failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Webhook test error: {e}")
        return False

def test_twilio_provision_endpoint(webhook_url):
    """Test the Twilio phone number provisioning endpoint"""
    print(f"\n🧪 Testing Twilio provision endpoint: {webhook_url}/twilio/provision")
    
    # This will fail without real Twilio credentials, but we can test the endpoint structure
    test_payload = {
        'business_id': 'test-business-id',
        'area_code': '415'
    }
    
    try:
        response = requests.post(
            f"{webhook_url}/twilio/provision",
            json=test_payload,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer test-token",  # This will fail auth, but that's expected
                "ngrok-skip-browser-warning": "true"
            },
            timeout=30
        )
        
        print(f"📞 Provision response status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        # We expect 401 (unauthorized) since we're using a fake token
        if response.status_code == 401:
            print("✅ Provision endpoint accessible (auth required as expected)")
            return True
        elif response.status_code == 500 and "Twilio integration not configured" in response.text:
            print("✅ Provision endpoint working (Twilio credentials needed)")
            return True
        else:
            print(f"⚠️  Unexpected response from provision endpoint")
            return False
            
    except Exception as e:
        print(f"❌ Provision endpoint test error: {e}")
        return False

def test_business_lookup():
    """Test business lookup functionality"""
    print(f"\n🧪 Testing business lookup by phone number...")
    
    try:
        from db_driver import get_driver
        driver = get_driver()
        
        # Test with existing business phone number
        business = driver.find_business_by_phone('+***********')
        
        if business:
            print(f"✅ Business lookup successful!")
            print(f"   Business: {business.get('name', 'Unknown')}")
            print(f"   Phone: {business.get('phone', 'Unknown')}")
            print(f"   ID: {business.get('id', 'Unknown')}")
            return True
        else:
            print(f"❌ No business found for phone +***********")
            return False
            
    except Exception as e:
        print(f"❌ Business lookup error: {e}")
        return False

def test_sip_integration():
    """Test SIP integration functionality"""
    print(f"\n🧪 Testing SIP integration...")
    
    try:
        from sip_integration import SIPIntegration
        sip = SIPIntegration()
        
        if sip.enabled:
            print("✅ SIP integration enabled")
            
            # Test call routing
            result = sip.route_call_to_agent('+**********', '7ac85a6f-e413-44f8-bf5f-d99be88b54a0')
            
            if 'error' not in result:
                print("✅ SIP call routing successful")
                print(f"   Room: {result.get('room_name', 'Unknown')}")
                print(f"   Agent PID: {result.get('agent_pid', 'Unknown')}")
                return True
            else:
                print(f"❌ SIP call routing failed: {result['error']}")
                return False
        else:
            print("❌ SIP integration not enabled")
            return False
            
    except Exception as e:
        print(f"❌ SIP integration test error: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive test of Twilio integration"""
    print("🧪 Comprehensive Twilio Integration Test")
    print("=" * 60)
    
    results = {}
    
    # Test 1: Flask backend
    print("\n1️⃣ Testing Flask Backend...")
    results['flask'] = test_flask_backend()
    
    # Test 2: ngrok tunnel
    print("\n2️⃣ Testing ngrok tunnel...")
    webhook_url = get_ngrok_url()
    results['ngrok'] = webhook_url is not None
    
    if not webhook_url:
        print("❌ Cannot continue without ngrok tunnel")
        return False
    
    # Test 3: Business lookup
    print("\n3️⃣ Testing business lookup...")
    results['business_lookup'] = test_business_lookup()
    
    # Test 4: SIP integration
    print("\n4️⃣ Testing SIP integration...")
    results['sip_integration'] = test_sip_integration()
    
    # Test 5: Twilio voice webhook
    print("\n5️⃣ Testing Twilio voice webhook...")
    results['twilio_voice'] = test_twilio_voice_webhook(webhook_url)
    
    # Test 6: Twilio provision endpoint
    print("\n6️⃣ Testing Twilio provision endpoint...")
    results['twilio_provision'] = test_twilio_provision_endpoint(webhook_url)
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name.replace('_', ' ').title()}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"\n🎉 All tests passed! Twilio integration is ready!")
        print(f"\n📝 Next steps:")
        print(f"   1. Get Twilio credentials from https://console.twilio.com/")
        print(f"   2. Update .env file with real credentials")
        print(f"   3. Run: python3 setup_twilio_integration.py")
        print(f"   4. Test with real phone number provisioning")
        return True
    else:
        print(f"\n⚠️  Some tests failed. Please fix issues before proceeding.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    exit(0 if success else 1)
