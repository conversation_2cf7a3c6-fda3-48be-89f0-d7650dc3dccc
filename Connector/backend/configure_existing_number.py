#!/usr/bin/env python3
"""
Configure existing phone number to route to Connecto AI agent
"""

import os
from dotenv import load_dotenv
from twilio.rest import Client

# Load environment variables
load_dotenv()

def configure_phone_number_webhook():
    """Configure existing phone number to use our webhook"""
    
    account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
    auth_token = os.environ.get('TWILIO_AUTH_TOKEN')
    webhook_url = os.environ.get('WEBHOOK_BASE_URL')
    
    if not all([account_sid, auth_token, webhook_url]):
        print("❌ Missing Twilio credentials or webhook URL")
        return False
    
    client = Client(account_sid, auth_token)
    
    print("🔍 Looking for your phone numbers...")
    
    # List all phone numbers in your account
    numbers = client.incoming_phone_numbers.list()
    
    if not numbers:
        print("❌ No phone numbers found in your Twilio account")
        print("💡 You need to purchase a phone number first")
        return False
    
    print(f"📞 Found {len(numbers)} phone number(s) in your account:")
    
    for i, number in enumerate(numbers, 1):
        print(f"   {i}. {number.phone_number}")
        print(f"      Current webhook: {number.voice_url or 'None'}")
        print(f"      SID: {number.sid}")
        print()
    
    # Configure each number to use our webhook
    webhook_endpoint = f"{webhook_url}/twilio/voice"
    
    print(f"🔧 Configuring all numbers to use webhook: {webhook_endpoint}")
    print()
    
    for number in numbers:
        try:
            # Update the phone number configuration
            number.update(
                voice_url=webhook_endpoint,
                voice_method="POST"
            )
            
            print(f"✅ Configured {number.phone_number}")
            print(f"   Webhook: {webhook_endpoint}")
            print(f"   Method: POST")
            print()
            
        except Exception as e:
            print(f"❌ Error configuring {number.phone_number}: {e}")
    
    return True

def test_webhook_with_existing_number():
    """Test the webhook with an existing number"""
    
    webhook_url = os.environ.get('WEBHOOK_BASE_URL')
    
    # Test with the Spanish number from your business
    test_number = "+***********"
    
    print(f"🧪 Testing webhook with business number: {test_number}")
    
    import requests
    
    # Simulate Twilio webhook call
    test_payload = {
        'From': '+***********',  # Your Twilio number
        'To': test_number,       # Your business number
        'CallSid': 'test-call-from-twilio-setup'
    }
    
    try:
        response = requests.post(
            f"{webhook_url}/twilio/voice",
            data=test_payload,
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "ngrok-skip-browser-warning": "true"
            },
            timeout=30
        )
        
        print(f"📞 Webhook response: {response.status_code}")
        print(f"📄 Response content: {response.text}")
        
        if response.status_code == 200 and 'xml' in response.headers.get('content-type', ''):
            print("✅ Webhook test successful! Your AI agent is ready to answer calls.")
            return True
        else:
            print("❌ Webhook test failed")
            return False
            
    except Exception as e:
        print(f"❌ Webhook test error: {e}")
        return False

def make_test_call():
    """Make a test call using Twilio API to your configured number"""
    
    account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
    auth_token = os.environ.get('TWILIO_AUTH_TOKEN')
    webhook_url = os.environ.get('WEBHOOK_BASE_URL')
    
    client = Client(account_sid, auth_token)
    
    # Get your Twilio phone numbers
    numbers = client.incoming_phone_numbers.list()
    
    if not numbers:
        print("❌ No Twilio phone numbers available for testing")
        return False
    
    twilio_number = numbers[0].phone_number
    business_number = "+***********"  # Your business number
    
    print(f"📞 Making test call:")
    print(f"   From: {twilio_number} (Twilio)")
    print(f"   To: {business_number} (Your business)")
    print(f"   Webhook: {webhook_url}/twilio/voice")
    print()
    
    try:
        call = client.calls.create(
            url=f"{webhook_url}/twilio/voice",
            to=business_number,
            from_=twilio_number
        )
        
        print(f"✅ Test call initiated!")
        print(f"   Call SID: {call.sid}")
        print(f"   Status: {call.status}")
        print()
        print("🎯 Your AI agent should now answer the call!")
        print("📱 Check your phone to see if the call comes through")
        
        return True
        
    except Exception as e:
        print(f"❌ Error making test call: {e}")
        return False

def main():
    print("🔧 Configure Existing Phone Number for Connecto AI")
    print("=" * 60)
    
    # Step 1: Configure webhook
    print("Step 1: Configuring phone number webhooks...")
    if not configure_phone_number_webhook():
        return False
    
    # Step 2: Test webhook
    print("Step 2: Testing webhook functionality...")
    if not test_webhook_with_existing_number():
        print("⚠️  Webhook test failed, but configuration is complete")
    
    # Step 3: Make test call
    print("Step 3: Making test call...")
    choice = input("Do you want to make a test call to your business number? (y/n): ").lower()
    
    if choice == 'y':
        make_test_call()
    
    print("\n🎉 Configuration complete!")
    print("\n📞 Your phone numbers are now configured to use your AI agent")
    print("🤖 Incoming calls will be handled by Connecto AI Voice Receptionist")
    print("\n💡 To test manually, call one of your configured numbers")

if __name__ == "__main__":
    main()
