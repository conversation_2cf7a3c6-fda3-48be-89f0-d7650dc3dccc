# 📞 Complete Twilio Account Setup Guide

## 🎯 Step-by-Step Twilio Setup

### **Step 1: Create Twilio Account**

1. **Go to Twilio**: https://www.twilio.com/
2. **Click "Sign up for free"**
3. **Fill out the form**:
   - First Name
   - Last Name
   - Email address
   - Password
   - Phone number (for verification)

4. **Verify your phone number**:
   - <PERSON><PERSON><PERSON> will send you a verification code
   - Enter the code to verify your account

5. **Complete the questionnaire**:
   - What are you building? → "Voice calling application"
   - Which Twilio products? → "Voice"
   - How do you want to code? → "With code"
   - What's your preferred language? → "Python"

### **Step 2: Get Your Credentials**

1. **Go to Console Dashboard**: https://console.twilio.com/
2. **Find your Account Info** (on the right side):
   - **Account SID**: Starts with "AC..." (like `ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`)
   - **Auth Token**: Click "Show" to reveal it

3. **Copy these credentials** - you'll need them for your `.env` file

### **Step 3: Add Trial Credit (Free)**

- **New accounts get $15 free credit** automatically
- This is enough for testing and initial development
- You can add more credit later when you go to production

### **Step 4: Configure Your Environment**

Update your `Connector/backend/.env` file:

```bash
# Twilio Configuration
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
WEBHOOK_BASE_URL=https://your-domain.com
```

**Replace with your actual credentials from Step 2!**

### **Step 5: Test Your Setup**

Run the setup script:

```bash
cd Connector/backend
python3 setup_twilio_integration.py
```

This will:
- Test your Twilio connection
- Provision a test phone number
- Create a test business
- Test the complete call flow

## 🔧 **Detailed Configuration Steps**

### **Understanding Twilio Pricing**

**Free Trial Includes:**
- $15 free credit
- Can purchase phone numbers (~$1/month each)
- Can make/receive calls (~$0.01-0.02 per minute)
- Perfect for testing and initial development

**Production Pricing:**
- Phone numbers: ~$1/month each
- Voice calls: ~$0.01-0.02 per minute
- SMS: ~$0.0075 per message
- Very cost-effective for SaaS platforms

### **Phone Number Types**

1. **Local Numbers**: Regular phone numbers in specific area codes
2. **Toll-Free Numbers**: 1-800, 1-888, etc. (more expensive)
3. **International Numbers**: Available in many countries

For your SaaS, **local numbers** are perfect and most cost-effective.

### **Webhook Configuration**

Twilio needs to know where to send incoming calls. Your webhook URL should be:

**Development**: `https://your-ngrok-url.ngrok.io/twilio/voice`
**Production**: `https://your-domain.com/twilio/voice`

## 🧪 **Testing Your Setup**

### **Test 1: Verify Credentials**

```bash
python3 -c "
from twilio.rest import Client
import os
from dotenv import load_dotenv

load_dotenv()
client = Client(os.environ['TWILIO_ACCOUNT_SID'], os.environ['TWILIO_AUTH_TOKEN'])
account = client.api.accounts(os.environ['TWILIO_ACCOUNT_SID']).fetch()
print(f'✅ Account: {account.friendly_name}')
print(f'✅ Status: {account.status}')
"
```

### **Test 2: List Available Numbers**

```bash
python3 -c "
from twilio.rest import Client
import os
from dotenv import load_dotenv

load_dotenv()
client = Client(os.environ['TWILIO_ACCOUNT_SID'], os.environ['TWILIO_AUTH_TOKEN'])
numbers = client.available_phone_numbers('US').local.list(limit=3)
print('📞 Available numbers:')
for number in numbers:
    print(f'   {number.phone_number}')
"
```

### **Test 3: Run Full Integration Test**

```bash
python3 test_twilio_endpoints.py
```

Should show: **6/6 tests passed**

## 🚀 **Production Setup**

### **Step 1: Upgrade Account (When Ready)**

1. Go to **Console → Billing**
2. Add payment method (credit card)
3. This removes trial restrictions:
   - Can call any number (not just verified ones)
   - No Twilio branding on calls
   - Higher rate limits

### **Step 2: Configure Production Webhook**

1. **Deploy your app** to a production server
2. **Get SSL certificate** (required for webhooks)
3. **Update WEBHOOK_BASE_URL** in production `.env`:
   ```bash
   WEBHOOK_BASE_URL=https://your-production-domain.com
   ```

### **Step 3: Purchase Phone Numbers**

Your app will automatically purchase numbers via the API:

```bash
POST /twilio/provision
{
  "business_id": "client-business-id",
  "area_code": "415"
}
```

## 🔍 **Troubleshooting**

### **Common Issues:**

1. **"Invalid credentials"**
   - Double-check Account SID and Auth Token
   - Make sure no extra spaces in `.env` file

2. **"Webhook not accessible"**
   - Ensure ngrok is running: `ngrok http 5001`
   - Check firewall settings

3. **"Insufficient funds"**
   - Add credit to your Twilio account
   - Check billing section in console

4. **"Number not available"**
   - Try different area codes
   - Some area codes have limited availability

### **Getting Help:**

- **Twilio Docs**: https://www.twilio.com/docs
- **Twilio Support**: Available in console
- **Community**: https://stackoverflow.com/questions/tagged/twilio

## 📋 **Quick Setup Checklist**

- [ ] Create Twilio account
- [ ] Verify phone number
- [ ] Get Account SID and Auth Token
- [ ] Update `.env` file with credentials
- [ ] Test connection with Python script
- [ ] Run `python3 test_twilio_endpoints.py`
- [ ] See 6/6 tests pass
- [ ] Run `python3 setup_twilio_integration.py` (optional, for real number)

## 🎉 **You're Ready!**

Once you complete these steps, your **Connecto AI Voice Receptionist** will have:

✅ **Automated phone number provisioning**
✅ **Multi-tenant call routing**  
✅ **Business-specific AI agents**
✅ **Scalable SaaS architecture**

**No manual configuration needed for new clients!** 🚀

---

## 💡 **Pro Tips**

1. **Start with trial** - $15 credit is plenty for testing
2. **Use local numbers** - Much cheaper than toll-free
3. **Monitor usage** - Check Twilio console for call logs
4. **Set up alerts** - Get notified when credit runs low
5. **Test thoroughly** - Use the provided test scripts

Your SaaS platform is ready to scale! 🎊
