#!/usr/bin/env python3
"""
Set up public webhook URL using ngrok for SIP integration
"""

import os
import subprocess
import time
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_ngrok_installed():
    """Check if ngrok is installed"""
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ ngrok is installed: {result.stdout.strip()}")
            return True
        else:
            print("❌ ngrok is not installed")
            return False
    except FileNotFoundError:
        print("❌ ngrok is not installed")
        return False

def install_ngrok():
    """Install ngrok using homebrew (macOS)"""
    print("📦 Installing ngrok...")
    try:
        # Install ngrok using homebrew
        subprocess.run(['brew', 'install', 'ngrok'], check=True)
        print("✅ ngrok installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install ngrok with homebrew")
        print("Please install ngrok manually from https://ngrok.com/download")
        return False
    except FileNotFoundError:
        print("❌ Homebrew not found. Please install ngrok manually from https://ngrok.com/download")
        return False

def start_ngrok_tunnel():
    """Start ngrok tunnel for the Flask backend"""
    print("🚇 Starting ngrok tunnel for port 5001...")
    
    try:
        # Start ngrok in background
        process = subprocess.Popen(
            ['ngrok', 'http', '5001', '--log=stdout'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit for ngrok to start
        time.sleep(3)
        
        # Get ngrok tunnel info
        try:
            response = requests.get('http://localhost:4040/api/tunnels')
            tunnels = response.json()
            
            if tunnels.get('tunnels'):
                tunnel = tunnels['tunnels'][0]
                public_url = tunnel['public_url']
                print(f"✅ ngrok tunnel started!")
                print(f"   Local URL: http://localhost:5001")
                print(f"   Public URL: {public_url}")
                print(f"   Webhook URL: {public_url}/sip/webhook")
                
                return {
                    'process': process,
                    'public_url': public_url,
                    'webhook_url': f"{public_url}/sip/webhook"
                }
            else:
                print("❌ No ngrok tunnels found")
                return None
                
        except requests.exceptions.ConnectionError:
            print("❌ Could not connect to ngrok API")
            return None
            
    except Exception as e:
        print(f"❌ Error starting ngrok: {e}")
        return None

def test_webhook_accessibility(webhook_url):
    """Test if the webhook is publicly accessible"""
    print(f"🧪 Testing webhook accessibility: {webhook_url}")
    
    try:
        # Test the health endpoint first
        health_url = webhook_url.replace('/sip/webhook', '/health')
        response = requests.get(health_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Webhook is publicly accessible!")
            return True
        else:
            print(f"❌ Webhook returned status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Webhook is not accessible: {e}")
        return False

def update_sip_configuration(webhook_url):
    """Update SIP configuration with the public webhook URL"""
    print(f"🔧 Updating SIP configuration with webhook: {webhook_url}")
    
    # This would update your LiveKit SIP configuration
    # For now, we'll just print the instructions
    print("\n📝 Manual Configuration Steps:")
    print("1. Go to your LiveKit Cloud dashboard")
    print("2. Navigate to SIP settings")
    print("3. Update your SIP dispatch rule with this webhook URL:")
    print(f"   {webhook_url}")
    print("4. Make sure your business phone numbers are added to the SIP trunk")
    
    # You can also save this to a config file
    config = {
        'webhook_url': webhook_url,
        'sip_trunk_id': os.environ.get('SIP_INBOUND_TRUNK_ID'),
        'livekit_url': os.environ.get('LIVEKIT_URL')
    }
    
    with open('sip_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"💾 Configuration saved to sip_config.json")

def main():
    print("🌐 Public Webhook Setup for SIP Integration")
    print("=" * 50)
    
    # Check if Flask backend is running
    try:
        response = requests.get('http://localhost:5001/health', timeout=5)
        if response.status_code == 200:
            print("✅ Flask backend is running on port 5001")
        else:
            print("❌ Flask backend is not responding properly")
            return False
    except requests.exceptions.RequestException:
        print("❌ Flask backend is not running on port 5001")
        print("Please start your Flask backend first: python3 api.py")
        return False
    
    # Check if ngrok is installed
    if not check_ngrok_installed():
        if not install_ngrok():
            return False
    
    # Start ngrok tunnel
    tunnel_info = start_ngrok_tunnel()
    if not tunnel_info:
        return False
    
    webhook_url = tunnel_info['webhook_url']
    
    # Test webhook accessibility
    if not test_webhook_accessibility(webhook_url):
        return False
    
    # Update SIP configuration
    update_sip_configuration(webhook_url)
    
    print("\n🎉 Setup completed successfully!")
    print("\n📞 How to test:")
    print("1. Call one of your business phone numbers")
    print("2. The call should be routed to your AI agent")
    print("3. Check the Flask logs for incoming webhook calls")
    
    print(f"\n⚠️  Keep this terminal open to maintain the ngrok tunnel")
    print(f"   Public webhook URL: {webhook_url}")
    
    # Keep the script running to maintain the tunnel
    try:
        print("\nPress Ctrl+C to stop the tunnel...")
        tunnel_info['process'].wait()
    except KeyboardInterrupt:
        print("\n🛑 Stopping ngrok tunnel...")
        tunnel_info['process'].terminate()
        print("✅ Tunnel stopped")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Setup failed!")
        exit(1)
