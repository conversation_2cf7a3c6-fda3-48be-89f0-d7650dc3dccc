#!/usr/bin/env python3
"""
Configure LiveKit SIP trunk and dispatch rules via API
"""

import os
import json
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def configure_livekit_sip():
    """Configure LiveKit SIP trunk and dispatch rules"""
    
    # Get configuration
    with open('sip_config.json', 'r') as f:
        config = json.load(f)
    
    webhook_url = config['webhook_url']
    sip_trunk_id = config['sip_trunk_id']
    
    print(f"🔧 Configuring LiveKit SIP Integration")
    print(f"   Webhook: {webhook_url}")
    print(f"   Trunk ID: {sip_trunk_id}")
    
    # Phone numbers to configure
    phone_numbers = ["+34 631789277", "+34631789277", "+1234567890"]
    
    print(f"\n📞 Phone numbers to configure:")
    for phone in phone_numbers:
        print(f"   - {phone}")
    
    print(f"\n📋 Manual Configuration Steps:")
    print(f"1. Go to LiveKit Cloud Dashboard: https://cloud.livekit.io/")
    print(f"2. Navigate to your project: {os.environ.get('LIVEKIT_URL', 'N/A')}")
    print(f"3. Go to SIP settings")
    print(f"4. Find SIP trunk: {sip_trunk_id}")
    print(f"5. Add these phone numbers to the trunk:")
    for phone in phone_numbers:
        print(f"   - {phone}")
    print(f"6. Update dispatch rule with webhook URL: {webhook_url}")
    print(f"7. Set dispatch rule pattern to: .* (match all)")
    print(f"8. Link dispatch rule to trunk: {sip_trunk_id}")
    
    print(f"\n🧪 Test Configuration:")
    print(f"1. Call any of your business phone numbers")
    print(f"2. The call should be routed to your AI agent")
    print(f"3. Check Flask logs for incoming webhook calls")
    
    print(f"\n⚠️  Important Notes:")
    print(f"- Keep ngrok tunnel running for webhook to work")
    print(f"- Each business gets its own AI agent with specific context")
    print(f"- Calls are logged in the database for each business")
    
    return True

if __name__ == "__main__":
    print("🌐 LiveKit SIP Configuration Guide")
    print("=" * 50)
    
    configure_livekit_sip()
    
    print(f"\n✅ Configuration guide completed!")
    print(f"🚀 Follow the manual steps above to enable incoming calls")
