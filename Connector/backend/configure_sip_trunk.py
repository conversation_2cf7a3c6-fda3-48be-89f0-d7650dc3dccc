#!/usr/bin/env python3
"""
Configure LiveKit SIP trunk for incoming call routing
"""

import os
import asyncio
from dotenv import load_dotenv
from livekit import api

# Load environment variables
load_dotenv()

async def configure_sip_trunk():
    """Configure LiveKit SIP trunk to route incoming calls to webhook"""

    # LiveKit credentials
    livekit_url = os.environ.get('LIVEKIT_URL')
    livekit_api_key = os.environ.get('LIVEKIT_API_KEY')
    livekit_api_secret = os.environ.get('LIVEKIT_API_SECRET')
    sip_trunk_id = os.environ.get('SIP_INBOUND_TRUNK_ID')

    if not all([livekit_url, livekit_api_key, livekit_api_secret]):
        print("❌ Missing LiveKit credentials")
        return False

    print(f"🔧 Configuring SIP trunk: {sip_trunk_id}")
    print(f"🌐 LiveKit URL: {livekit_url}")

    # Create LiveKit API client
    livekit_api = api.LiveKitAPI(
        url=livekit_url,
        api_key=livekit_api_key,
        api_secret=livekit_api_secret
    )

    try:
        # List existing SIP trunks
        print("\n📋 Listing existing SIP trunks...")
        try:
            from livekit.api import ListSIPInboundTrunkRequest
            trunks_response = await livekit_api.sip.list_sip_inbound_trunk(
                ListSIPInboundTrunkRequest()
            )
            trunks = trunks_response.items
        except Exception as e:
            print(f"Error listing SIP trunks: {e}")
            print("This might be due to API version differences or permissions.")
            trunks = []

        print(f"Found {len(trunks)} SIP trunk(s):")
        for trunk in trunks:
            print(f"  - ID: {trunk.sip_trunk_id}")
            print(f"    Name: {getattr(trunk, 'name', 'N/A')}")
            print(f"    Numbers: {getattr(trunk, 'numbers', 'N/A')}")
            print()

        # Check if our trunk exists
        our_trunk = None
        for trunk in trunks:
            if trunk.sip_trunk_id == sip_trunk_id:
                our_trunk = trunk
                break

        if our_trunk:
            print(f"✅ Found existing SIP trunk: {sip_trunk_id}")
        else:
            print(f"❌ SIP trunk {sip_trunk_id} not found")
            if trunks:
                print("Available trunks:")
                for trunk in trunks:
                    print(f"  - {trunk.sip_trunk_id}")

        # List SIP dispatch rules
        print("\n📋 Listing SIP dispatch rules...")
        try:
            from livekit.api import ListSIPDispatchRuleRequest
            rules_response = await livekit_api.sip.list_sip_dispatch_rule(
                ListSIPDispatchRuleRequest()
            )
            rules = rules_response.items

            print(f"Found {len(rules)} dispatch rule(s):")
            for rule in rules:
                print(f"  - ID: {rule.sip_dispatch_rule_id}")
                print(f"    Rule: {getattr(rule, 'rule', 'N/A')}")
                print(f"    Trunk IDs: {getattr(rule, 'trunk_ids', 'N/A')}")
                print(f"    Room Name: {getattr(rule, 'room_name', 'N/A')}")
                print()
        except Exception as e:
            print(f"Error listing dispatch rules: {e}")

        return True

    except Exception as e:
        print(f"❌ Error configuring SIP trunk: {e}")
        return False

async def create_dispatch_rule():
    """Create a SIP dispatch rule to route calls to rooms"""

    livekit_url = os.environ.get('LIVEKIT_URL')
    livekit_api_key = os.environ.get('LIVEKIT_API_KEY')
    livekit_api_secret = os.environ.get('LIVEKIT_API_SECRET')
    sip_trunk_id = os.environ.get('SIP_INBOUND_TRUNK_ID')

    livekit_api = api.LiveKitAPI(
        url=livekit_url,
        api_key=livekit_api_key,
        api_secret=livekit_api_secret
    )

    try:
        # Create dispatch rule for routing calls
        print(f"\n🔧 Creating SIP dispatch rule...")

        from livekit.api import CreateSIPDispatchRuleRequest

        # This rule will route all incoming calls to a room based on the called number
        rule_response = await livekit_api.sip.create_sip_dispatch_rule(
            CreateSIPDispatchRuleRequest(
                rule=".*",  # Match all incoming numbers
                trunk_ids=[sip_trunk_id],
                room_name="sip-call-{to}",  # Room name based on called number
                metadata={
                    "webhook_url": "https://your-domain.com/sip/webhook",  # Replace with your webhook URL
                    "description": "Route incoming calls to Connecto AI agents"
                }
            )
        )

        print(f"✅ Created dispatch rule: {rule_response.sip_dispatch_rule_id}")
        return True

    except Exception as e:
        print(f"❌ Error creating dispatch rule: {e}")
        return False

if __name__ == "__main__":
    print("🎯 LiveKit SIP Trunk Configuration Tool")
    print("=" * 50)

    # Run configuration
    success = asyncio.run(configure_sip_trunk())

    if success:
        print("\n✅ SIP trunk configuration completed!")
        print("\n📝 Next steps:")
        print("1. Make sure your webhook URL is publicly accessible")
        print("2. Update the dispatch rule with your actual webhook URL")
        print("3. Test incoming calls to your business phone numbers")
    else:
        print("\n❌ SIP trunk configuration failed!")
