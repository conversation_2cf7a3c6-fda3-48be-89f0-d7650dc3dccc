#!/usr/bin/env python3
"""
Test script for SIP integration endpoint
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5001"

def test_sip_endpoint():
    """Test the SIP integration endpoint with proper authentication"""
    
    print("🔐 Step 1: Logging in to get authentication token...")
    
    # Login to get a valid token
    login_data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        login_response = requests.post(
            f"{BASE_URL}/users/login",
            json=login_data,
            timeout=30
        )
        
        print(f"Login response status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.text}")
            return False
            
        login_result = login_response.json()
        
        if not login_result.get("success"):
            print(f"❌ Login failed: {login_result.get('message')}")
            return False
            
        access_token = login_result["data"]["access_token"]
        user_id = login_result["data"]["user"]["id"]
        print(f"✅ Login successful! User ID: {user_id}")
        
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return False
    
    print("\n🏢 Step 2: Creating a test business...")
    
    # Create a test business
    business_data = {
        "name": "Test SIP Business",
        "address": "123 Test Street, Test City",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "business_type": "restaurant"
    }
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        business_response = requests.post(
            f"{BASE_URL}/businesses",
            json=business_data,
            headers=headers,
            timeout=30
        )
        
        print(f"Business creation response status: {business_response.status_code}")
        
        if business_response.status_code != 201:
            print(f"❌ Business creation failed: {business_response.text}")
            return False
            
        business_result = business_response.json()
        
        if not business_result.get("success"):
            print(f"❌ Business creation failed: {business_result.get('message')}")
            return False
            
        business_id = business_result["data"]["id"]
        print(f"✅ Business created! Business ID: {business_id}")
        
    except Exception as e:
        print(f"❌ Business creation error: {str(e)}")
        return False
    
    print("\n📞 Step 3: Testing SIP integration...")
    
    # Test SIP integration
    sip_test_data = {
        "business_id": business_id,
        "test_number": "+1234567890"
    }
    
    try:
        sip_response = requests.post(
            f"{BASE_URL}/sip/test-call",
            json=sip_test_data,
            headers=headers,
            timeout=30
        )
        
        print(f"SIP test response status: {sip_response.status_code}")
        print(f"SIP test response: {sip_response.text}")
        
        if sip_response.status_code == 200:
            sip_result = sip_response.json()
            if sip_result.get("success"):
                print("✅ SIP integration test successful!")
                print(f"Room name: {sip_result['data'].get('room_name')}")
                print(f"Room URL: {sip_result['data'].get('room_url')}")
                return True
            else:
                print(f"❌ SIP test failed: {sip_result.get('message')}")
                return False
        else:
            print(f"❌ SIP test failed with status {sip_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ SIP test error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing SIP Integration Endpoint\n")
    
    success = test_sip_endpoint()
    
    if success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Tests failed!")
        sys.exit(1)
