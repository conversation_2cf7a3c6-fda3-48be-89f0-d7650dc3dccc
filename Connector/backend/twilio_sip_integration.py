#!/usr/bin/env python3
"""
Twilio SIP Integration for Multi-Tenant Voice Receptionist
This approach eliminates the need to update LiveKit for each new client
"""

import os
import logging
from typing import Dict, Any, Optional
from twilio.rest import Client
from twilio.twiml import VoiceResponse
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class TwilioSIPIntegration:
    """
    Twilio SIP integration for multi-tenant voice receptionist.
    Clients can use their own phone numbers without updating LiveKit.
    """
    
    def __init__(self):
        self.account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
        self.auth_token = os.environ.get('TWILIO_AUTH_TOKEN')
        self.webhook_base_url = os.environ.get('WEBHOOK_BASE_URL')  # Your public webhook URL
        
        self.enabled = bool(self.account_sid and self.auth_token and self.webhook_base_url)
        
        if self.enabled:
            self.client = Client(self.account_sid, self.auth_token)
            logger.info("Twilio SIP integration enabled")
        else:
            logger.warning("Twilio SIP integration disabled - missing credentials")
    
    def provision_phone_number(self, business_id: str, area_code: str = None, country: str = "US") -> Dict[str, Any]:
        """
        Provision a new phone number for a business.
        
        Args:
            business_id: Business ID to associate with the number
            area_code: Preferred area code (optional)
            country: Country code for the number
            
        Returns:
            Dict with phone number details
        """
        if not self.enabled:
            return {"error": "Twilio integration not configured"}
        
        try:
            # Search for available phone numbers
            search_params = {"country": country}
            if area_code:
                search_params["area_code"] = area_code
            
            available_numbers = self.client.available_phone_numbers(country).local.list(**search_params, limit=1)
            
            if not available_numbers:
                return {"error": f"No available phone numbers in {area_code or country}"}
            
            # Purchase the phone number
            phone_number = available_numbers[0].phone_number
            
            purchased_number = self.client.incoming_phone_numbers.create(
                phone_number=phone_number,
                voice_url=f"{self.webhook_base_url}/twilio/voice",  # Your voice webhook
                voice_method="POST"
            )
            
            logger.info(f"Provisioned phone number {phone_number} for business {business_id}")
            
            return {
                "success": True,
                "phone_number": phone_number,
                "sid": purchased_number.sid,
                "business_id": business_id
            }
            
        except Exception as e:
            logger.error(f"Error provisioning phone number: {e}")
            return {"error": str(e)}
    
    def configure_existing_number(self, phone_number: str, business_id: str) -> Dict[str, Any]:
        """
        Configure an existing Twilio number to route to our webhook.
        
        Args:
            phone_number: Existing Twilio phone number
            business_id: Business ID to associate
            
        Returns:
            Dict with configuration result
        """
        if not self.enabled:
            return {"error": "Twilio integration not configured"}
        
        try:
            # Find the phone number in Twilio
            numbers = self.client.incoming_phone_numbers.list(phone_number=phone_number)
            
            if not numbers:
                return {"error": f"Phone number {phone_number} not found in your Twilio account"}
            
            # Update the webhook URL
            number = numbers[0]
            number.update(
                voice_url=f"{self.webhook_base_url}/twilio/voice",
                voice_method="POST"
            )
            
            logger.info(f"Configured existing number {phone_number} for business {business_id}")
            
            return {
                "success": True,
                "phone_number": phone_number,
                "business_id": business_id,
                "message": "Number configured successfully"
            }
            
        except Exception as e:
            logger.error(f"Error configuring existing number: {e}")
            return {"error": str(e)}
    
    def handle_incoming_call(self, from_number: str, to_number: str, call_sid: str) -> str:
        """
        Handle incoming call and generate TwiML to connect to LiveKit.
        
        Args:
            from_number: Caller's phone number
            to_number: Called phone number (business number)
            call_sid: Twilio call SID
            
        Returns:
            TwiML response string
        """
        try:
            from db_driver import get_driver
            from sip_integration import SIPIntegration
            
            # Find business by phone number
            driver = get_driver()
            business = driver.find_business_by_phone(to_number)
            
            if not business:
                logger.warning(f"No business found for phone number: {to_number}")
                # Return TwiML for "number not in service"
                response = VoiceResponse()
                response.say("Sorry, this number is not in service.", voice="alice")
                response.hangup()
                return str(response)
            
            # Create LiveKit room and start agent
            sip = SIPIntegration()
            call_info = sip.route_call_to_agent(from_number, business['id'])
            
            if 'error' in call_info:
                logger.error(f"Failed to route call: {call_info['error']}")
                response = VoiceResponse()
                response.say("Sorry, we're experiencing technical difficulties. Please try again later.", voice="alice")
                response.hangup()
                return str(response)
            
            # Generate TwiML to connect to LiveKit room
            response = VoiceResponse()
            
            # Connect to LiveKit room via SIP
            # This requires LiveKit SIP endpoint configuration
            room_name = call_info['room_name']
            livekit_sip_endpoint = f"sip:{room_name}@{os.environ.get('LIVEKIT_SIP_DOMAIN', 'sip.livekit.io')}"
            
            dial = response.dial()
            dial.sip(livekit_sip_endpoint)
            
            logger.info(f"Routing call {call_sid} to LiveKit room: {room_name}")
            
            return str(response)
            
        except Exception as e:
            logger.error(f"Error handling incoming call: {e}")
            response = VoiceResponse()
            response.say("Sorry, we're experiencing technical difficulties.", voice="alice")
            response.hangup()
            return str(response)

# Global instance
twilio_sip = TwilioSIPIntegration()

def provision_number_for_business(business_id: str, area_code: str = None) -> Dict[str, Any]:
    """Provision a new phone number for a business"""
    return twilio_sip.provision_phone_number(business_id, area_code)

def configure_business_number(phone_number: str, business_id: str) -> Dict[str, Any]:
    """Configure existing number for a business"""
    return twilio_sip.configure_existing_number(phone_number, business_id)

def handle_twilio_webhook(from_number: str, to_number: str, call_sid: str) -> str:
    """Handle Twilio voice webhook"""
    return twilio_sip.handle_incoming_call(from_number, to_number, call_sid)
