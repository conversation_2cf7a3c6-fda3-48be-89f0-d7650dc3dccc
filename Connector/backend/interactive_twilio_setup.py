#!/usr/bin/env python3
"""
Interactive Twilio Setup Guide for Connecto
"""

import os
import sys
import time
from dotenv import load_dotenv

def print_header():
    print("🎯 Interactive Twilio Setup for Connecto AI Voice Receptionist")
    print("=" * 70)
    print()

def check_current_setup():
    """Check current Twilio configuration"""
    load_dotenv()
    
    account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
    auth_token = os.environ.get('TWILIO_AUTH_TOKEN')
    webhook_url = os.environ.get('WEBHOOK_BASE_URL')
    
    print("📋 Current Configuration Status:")
    print("-" * 40)
    
    if account_sid and account_sid != 'your_twilio_account_sid_here':
        print(f"✅ Account SID: {account_sid[:8]}...")
        sid_configured = True
    else:
        print("❌ Account SID: Not configured")
        sid_configured = False
    
    if auth_token and auth_token != 'your_twilio_auth_token_here':
        print(f"✅ Auth Token: {auth_token[:8]}...")
        token_configured = True
    else:
        print("❌ Auth Token: Not configured")
        token_configured = False
    
    if webhook_url and webhook_url != 'https://your-domain.com':
        print(f"✅ Webhook URL: {webhook_url}")
        webhook_configured = True
    else:
        print("❌ Webhook URL: Not configured")
        webhook_configured = False
    
    print()
    
    return sid_configured and token_configured, webhook_configured

def guide_account_creation():
    """Guide user through Twilio account creation"""
    print("🆕 Step 1: Create Twilio Account")
    print("-" * 40)
    print("1. Go to: https://www.twilio.com/")
    print("2. Click 'Sign up for free'")
    print("3. Fill out the registration form")
    print("4. Verify your phone number")
    print("5. Complete the questionnaire:")
    print("   - What are you building? → 'Voice calling application'")
    print("   - Which products? → 'Voice'")
    print("   - How to code? → 'With code'")
    print("   - Language? → 'Python'")
    print()
    
    input("Press Enter when you've completed account creation...")
    print()

def guide_credential_retrieval():
    """Guide user through getting credentials"""
    print("🔑 Step 2: Get Your Credentials")
    print("-" * 40)
    print("1. Go to: https://console.twilio.com/")
    print("2. Look for 'Account Info' on the right side")
    print("3. Copy your Account SID (starts with 'AC...')")
    print("4. Click 'Show' next to Auth Token and copy it")
    print()
    
    print("📝 Now let's add them to your configuration:")
    print()
    
    account_sid = input("Enter your Account SID: ").strip()
    auth_token = input("Enter your Auth Token: ").strip()
    
    if not account_sid.startswith('AC') or len(account_sid) != 34:
        print("⚠️  Warning: Account SID should start with 'AC' and be 34 characters long")
    
    if len(auth_token) != 32:
        print("⚠️  Warning: Auth Token should be 32 characters long")
    
    return account_sid, auth_token

def update_env_file(account_sid, auth_token):
    """Update the .env file with Twilio credentials"""
    print("\n💾 Updating .env file...")
    
    # Read current .env file
    env_path = '.env'
    with open(env_path, 'r') as f:
        lines = f.readlines()
    
    # Update Twilio credentials
    updated_lines = []
    for line in lines:
        if line.startswith('TWILIO_ACCOUNT_SID='):
            updated_lines.append(f'TWILIO_ACCOUNT_SID={account_sid}\n')
        elif line.startswith('TWILIO_AUTH_TOKEN='):
            updated_lines.append(f'TWILIO_AUTH_TOKEN={auth_token}\n')
        else:
            updated_lines.append(line)
    
    # Write back to .env file
    with open(env_path, 'w') as f:
        f.writelines(updated_lines)
    
    print("✅ .env file updated successfully!")
    print()

def test_twilio_connection():
    """Test the Twilio connection"""
    print("🧪 Step 3: Testing Twilio Connection")
    print("-" * 40)
    
    try:
        from twilio.rest import Client
        load_dotenv()  # Reload environment variables
        
        account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
        auth_token = os.environ.get('TWILIO_AUTH_TOKEN')
        
        print("Connecting to Twilio...")
        client = Client(account_sid, auth_token)
        
        # Test connection by fetching account info
        account = client.api.accounts(account_sid).fetch()
        
        print(f"✅ Connection successful!")
        print(f"   Account Name: {account.friendly_name}")
        print(f"   Account Status: {account.status}")
        print(f"   Account Type: {account.type}")
        
        # Check account balance
        try:
            balance = client.balance.fetch()
            print(f"   Account Balance: ${balance.balance} {balance.currency}")
        except:
            print("   Account Balance: Unable to fetch (trial account)")
        
        print()
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("Please check your credentials and try again.")
        print()
        return False

def check_available_numbers():
    """Check available phone numbers"""
    print("📞 Step 4: Checking Available Phone Numbers")
    print("-" * 40)
    
    try:
        from twilio.rest import Client
        load_dotenv()
        
        client = Client(os.environ.get('TWILIO_ACCOUNT_SID'), os.environ.get('TWILIO_AUTH_TOKEN'))
        
        print("Searching for available phone numbers...")
        numbers = client.available_phone_numbers('US').local.list(limit=5)
        
        if numbers:
            print("✅ Available phone numbers found:")
            for i, number in enumerate(numbers, 1):
                print(f"   {i}. {number.phone_number} ({number.locality}, {number.region})")
            print()
            return True
        else:
            print("❌ No available phone numbers found")
            return False
            
    except Exception as e:
        print(f"❌ Error checking numbers: {e}")
        return False

def run_integration_tests():
    """Run the integration tests"""
    print("🧪 Step 5: Running Integration Tests")
    print("-" * 40)
    
    print("Running comprehensive test suite...")
    print()
    
    import subprocess
    try:
        result = subprocess.run(['python3', 'test_twilio_endpoints.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ All integration tests passed!")
            print()
            # Show last few lines of output
            lines = result.stdout.strip().split('\n')
            for line in lines[-10:]:
                if 'PASS' in line or 'tests passed' in line or '✅' in line:
                    print(f"   {line}")
            print()
            return True
        else:
            print("❌ Some tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Tests timed out")
        return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def show_next_steps():
    """Show next steps for production"""
    print("🚀 Next Steps for Production")
    print("-" * 40)
    print("1. 💳 Upgrade Twilio Account:")
    print("   - Go to Console → Billing")
    print("   - Add payment method to remove trial restrictions")
    print()
    print("2. 🌐 Deploy to Production:")
    print("   - Deploy your app to a production server")
    print("   - Get SSL certificate (required for webhooks)")
    print("   - Update WEBHOOK_BASE_URL in production .env")
    print()
    print("3. 📱 Test with Real Calls:")
    print("   - Run: python3 setup_twilio_integration.py")
    print("   - This will provision a real phone number")
    print("   - Test by calling the number")
    print()
    print("4. 🎯 Launch Your SaaS:")
    print("   - Update frontend to use /twilio/provision endpoint")
    print("   - Clients can now get phone numbers automatically!")
    print()

def main():
    print_header()
    
    # Check current setup
    credentials_configured, webhook_configured = check_current_setup()
    
    if credentials_configured:
        print("✅ Twilio credentials already configured!")
        print()
        
        # Test connection
        if test_twilio_connection():
            # Check numbers
            check_available_numbers()
            
            # Run tests
            if run_integration_tests():
                print("🎉 Twilio setup is complete and working!")
                print()
                show_next_steps()
            else:
                print("⚠️  Setup complete but tests failed. Check the issues above.")
        else:
            print("❌ Credentials configured but connection failed.")
            print("Please check your Account SID and Auth Token.")
    else:
        print("🔧 Let's set up your Twilio integration!")
        print()
        
        # Guide through account creation
        guide_account_creation()
        
        # Get credentials
        account_sid, auth_token = guide_credential_retrieval()
        
        # Update .env file
        update_env_file(account_sid, auth_token)
        
        # Test connection
        if test_twilio_connection():
            # Check numbers
            check_available_numbers()
            
            # Run tests
            if run_integration_tests():
                print("🎉 Twilio setup completed successfully!")
                print()
                show_next_steps()
            else:
                print("⚠️  Setup complete but some tests failed.")
                print("Your credentials work, but there might be other issues.")
        else:
            print("❌ Setup failed. Please check your credentials and try again.")

if __name__ == "__main__":
    main()
