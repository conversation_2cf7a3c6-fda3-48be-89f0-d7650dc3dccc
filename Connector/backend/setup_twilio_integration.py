#!/usr/bin/env python3
"""
Complete Twilio Integration Setup and Testing for Connecto Multi-Tenant SaaS
"""

import os
import json
import requests
import time
from dotenv import load_dotenv
from db_driver import get_driver

# Load environment variables
load_dotenv()

class TwilioSetupManager:
    """Manages Twilio integration setup and testing"""
    
    def __init__(self):
        self.account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
        self.auth_token = os.environ.get('TWILIO_AUTH_TOKEN')
        self.webhook_base_url = os.environ.get('WEBHOOK_BASE_URL')
        self.flask_running = False
        self.ngrok_running = False
        
    def check_prerequisites(self):
        """Check if all prerequisites are met"""
        print("🔍 Checking Prerequisites...")
        
        issues = []
        
        # Check Twilio credentials
        if not self.account_sid or self.account_sid == 'your_twilio_account_sid_here':
            issues.append("❌ Twilio Account SID not configured")
        else:
            print(f"✅ Twilio Account SID: {self.account_sid[:8]}...")
            
        if not self.auth_token or self.auth_token == 'your_twilio_auth_token_here':
            issues.append("❌ Twilio Auth Token not configured")
        else:
            print(f"✅ Twilio Auth Token: {self.auth_token[:8]}...")
        
        # Check Flask backend
        try:
            response = requests.get('http://localhost:5001/health', timeout=5)
            if response.status_code == 200:
                print("✅ Flask backend is running")
                self.flask_running = True
            else:
                issues.append("❌ Flask backend not responding properly")
        except requests.exceptions.RequestException:
            issues.append("❌ Flask backend not running on port 5001")
        
        # Check ngrok tunnel
        try:
            response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
            tunnels = response.json()
            if tunnels.get('tunnels'):
                tunnel = tunnels['tunnels'][0]
                public_url = tunnel['public_url']
                print(f"✅ ngrok tunnel active: {public_url}")
                self.webhook_base_url = public_url
                self.ngrok_running = True
            else:
                issues.append("❌ No ngrok tunnels found")
        except requests.exceptions.RequestException:
            issues.append("❌ ngrok not running (needed for webhook)")
        
        # Check database connection
        try:
            driver = get_driver()
            businesses = driver.client.table("businesses").select("id").limit(1).execute()
            print("✅ Database connection working")
        except Exception as e:
            issues.append(f"❌ Database connection failed: {e}")
        
        return issues
    
    def setup_twilio_credentials_guide(self):
        """Provide guide for setting up Twilio credentials"""
        print("\n📋 Twilio Setup Guide")
        print("=" * 50)
        print("1. Go to https://console.twilio.com/")
        print("2. Sign up for a free account (gets $15 credit)")
        print("3. Go to Console Dashboard")
        print("4. Find your Account SID and Auth Token")
        print("5. Update your .env file:")
        print("   TWILIO_ACCOUNT_SID=your_actual_account_sid")
        print("   TWILIO_AUTH_TOKEN=your_actual_auth_token")
        print("6. Re-run this script")
        
    def test_twilio_connection(self):
        """Test Twilio API connection"""
        if not self.account_sid or not self.auth_token:
            return False
            
        try:
            from twilio.rest import Client
            client = Client(self.account_sid, self.auth_token)
            
            # Test by getting account info
            account = client.api.accounts(self.account_sid).fetch()
            print(f"✅ Twilio connection successful!")
            print(f"   Account: {account.friendly_name}")
            print(f"   Status: {account.status}")
            return True
            
        except Exception as e:
            print(f"❌ Twilio connection failed: {e}")
            return False
    
    def provision_test_phone_number(self):
        """Provision a test phone number for testing"""
        if not self.account_sid or not self.auth_token:
            print("❌ Twilio credentials not configured")
            return None
            
        try:
            from twilio.rest import Client
            client = Client(self.account_sid, self.auth_token)
            
            print("🔍 Searching for available phone numbers...")
            
            # Search for available numbers in US
            available_numbers = client.available_phone_numbers('US').local.list(limit=1)
            
            if not available_numbers:
                print("❌ No available phone numbers found")
                return None
            
            phone_number = available_numbers[0].phone_number
            print(f"📞 Found available number: {phone_number}")
            
            # Purchase the number
            print("💳 Purchasing phone number...")
            purchased_number = client.incoming_phone_numbers.create(
                phone_number=phone_number,
                voice_url=f"{self.webhook_base_url}/twilio/voice",
                voice_method="POST"
            )
            
            print(f"✅ Phone number purchased successfully!")
            print(f"   Number: {phone_number}")
            print(f"   SID: {purchased_number.sid}")
            print(f"   Webhook: {self.webhook_base_url}/twilio/voice")
            
            return {
                'phone_number': phone_number,
                'sid': purchased_number.sid,
                'webhook_url': f"{self.webhook_base_url}/twilio/voice"
            }
            
        except Exception as e:
            print(f"❌ Error provisioning phone number: {e}")
            return None
    
    def create_test_business(self, phone_number):
        """Create a test business with the provisioned phone number"""
        try:
            driver = get_driver()
            
            # Create test business
            business_data = {
                "name": "Twilio Test Business",
                "phone": phone_number,
                "email": "<EMAIL>",
                "address": "123 Test Street, Test City",
                "business_type": "restaurant"
            }
            
            # Use existing user (get first user)
            users = driver.client.table("auth.users").select("id").limit(1).execute()
            if not users.data:
                print("❌ No users found in database")
                return None
            
            user_id = users.data[0]['id']
            
            business = driver.create_business(user_id, business_data)
            
            print(f"✅ Test business created!")
            print(f"   Business ID: {business['id']}")
            print(f"   Name: {business['name']}")
            print(f"   Phone: {business['phone']}")
            
            return business
            
        except Exception as e:
            print(f"❌ Error creating test business: {e}")
            return None
    
    def test_webhook_call_flow(self, phone_number, business_id):
        """Test the complete call flow via webhook"""
        print(f"\n🧪 Testing webhook call flow...")
        
        # Simulate Twilio webhook call
        webhook_url = f"{self.webhook_base_url}/twilio/voice"
        
        test_payload = {
            'From': '+1234567890',  # Caller
            'To': phone_number,     # Business number
            'CallSid': f'test-call-{int(time.time())}'
        }
        
        try:
            print(f"📞 Simulating call to {phone_number}...")
            response = requests.post(
                webhook_url,
                data=test_payload,  # Twilio sends form data
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "ngrok-skip-browser-warning": "true"
                },
                timeout=30
            )
            
            print(f"📄 Webhook response: {response.status_code}")
            print(f"📄 Response content: {response.text}")
            
            if response.status_code == 200:
                print("✅ Webhook call flow test successful!")
                return True
            else:
                print(f"❌ Webhook test failed with status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Webhook test error: {e}")
            return False
    
    def run_complete_setup_and_test(self):
        """Run the complete setup and testing process"""
        print("🚀 Twilio Integration Setup & Testing")
        print("=" * 60)
        
        # Check prerequisites
        issues = self.check_prerequisites()
        
        if issues:
            print("\n❌ Prerequisites not met:")
            for issue in issues:
                print(f"   {issue}")
            
            if any("Twilio" in issue for issue in issues):
                self.setup_twilio_credentials_guide()
            
            if any("Flask" in issue for issue in issues):
                print("\n💡 Start Flask backend: python3 api.py")
            
            if any("ngrok" in issue for issue in issues):
                print("\n💡 Start ngrok tunnel: ngrok http 5001")
            
            return False
        
        print("\n✅ All prerequisites met!")
        
        # Test Twilio connection
        print(f"\n🔗 Testing Twilio connection...")
        if not self.test_twilio_connection():
            return False
        
        # Provision test phone number
        print(f"\n📞 Provisioning test phone number...")
        phone_info = self.provision_test_phone_number()
        if not phone_info:
            return False
        
        # Create test business
        print(f"\n🏢 Creating test business...")
        business = self.create_test_business(phone_info['phone_number'])
        if not business:
            return False
        
        # Test webhook call flow
        print(f"\n🧪 Testing complete call flow...")
        webhook_success = self.test_webhook_call_flow(
            phone_info['phone_number'], 
            business['id']
        )
        
        if webhook_success:
            print(f"\n🎉 Twilio Integration Setup Complete!")
            print(f"\n📋 Test Results:")
            print(f"   ✅ Twilio connection: Working")
            print(f"   ✅ Phone number: {phone_info['phone_number']}")
            print(f"   ✅ Business created: {business['name']}")
            print(f"   ✅ Webhook flow: Working")
            print(f"   ✅ Multi-tenant routing: Working")
            
            print(f"\n🚀 Ready for Production!")
            print(f"   - Clients can now get phone numbers automatically")
            print(f"   - No manual LiveKit configuration needed")
            print(f"   - Fully scalable multi-tenant architecture")
            
            return True
        else:
            print(f"\n❌ Setup completed but webhook test failed")
            return False

def main():
    setup_manager = TwilioSetupManager()
    success = setup_manager.run_complete_setup_and_test()
    
    if success:
        print(f"\n✅ Twilio integration ready for production!")
    else:
        print(f"\n❌ Setup incomplete. Please fix issues and try again.")
    
    return success

if __name__ == "__main__":
    main()
